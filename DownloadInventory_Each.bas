Function CreateFoldersInPath(location As String) As Boolean
    Dim folders() As String
    Dim folderPath As String
    Dim folder As String
    Dim i As Integer
     
    If location <> "" Then
    
        folders = Split(location, "\")
        folderPath = folders(0) & "\"
        
        For i = 1 To UBound(folders)
            folder = folders(i)
            If folder <> "" Then
                folderPath = folderPath & folder & "\"
                If Dir(folderPath, vbDirectory) = "" Then
                    MkDir folderPath
                End If
            End If
        Next i
    
    End If
    
End Function
Function GetFolderFromPath(myNameSpace As Outlook.nameSpace, folderPath As String) As Outlook.folder
    Dim FoldersArray As Variant
    Dim i As Integer
    Dim folder As Outlook.folder
    Dim j As Integer
    Dim availableFolders As String

    ' 移除路径开头的两个反斜杠（如果存在）
    If Left(folderPath, 2) = "\\" Then
        folderPath = Right(folderPath, Len(folderPath) - 2)
    End If
    FoldersArray = Split(folderPath, "\")

    On Error GoTo ErrorHandler

    ' 调试：显示所有可用的根文件夹
    Debug.Print "=== 可用的根文件夹 ==="
    For j = 1 To myNameSpace.folders.Count
        Debug.Print j & ": " & myNameSpace.folders(j).Name
        availableFolders = availableFolders & myNameSpace.folders(j).Name & "; "
    Next j
    Debug.Print "查找的文件夹名: " & FoldersArray(0)

    ' 尝试通过名称匹配找到根文件夹
    Set folder = Nothing
    For j = 1 To myNameSpace.folders.Count
        If myNameSpace.folders(j).Name = FoldersArray(0) Then
            Set folder = myNameSpace.folders(j)
            Debug.Print "找到匹配的根文件夹: " & folder.Name
            Exit For
        End If
    Next j

    ' 如果没有找到精确匹配，尝试部分匹配
    If folder Is Nothing Then
        For j = 1 To myNameSpace.folders.Count
            If InStr(1, myNameSpace.folders(j).Name, FoldersArray(0), vbTextCompare) > 0 Then
                Set folder = myNameSpace.folders(j)
                Debug.Print "找到部分匹配的根文件夹: " & folder.Name
                Exit For
            End If
        Next j
    End If

    ' 如果仍然没有找到，使用默认邮箱
    If folder Is Nothing Then
        Debug.Print "未找到指定文件夹，使用默认邮箱"
        Set folder = myNameSpace.GetDefaultFolder(olFolderInbox).Parent
        Debug.Print "默认邮箱名称: " & folder.Name
    End If

    ' 遍历余下的文件夹层级
    If Not folder Is Nothing And UBound(FoldersArray) > 0 Then
        For i = 1 To UBound(FoldersArray, 1)
            Debug.Print "查找子文件夹: " & FoldersArray(i)
            Set folder = folder.folders(FoldersArray(i))
            If folder Is Nothing Then
                Debug.Print "未找到子文件夹: " & FoldersArray(i)
                Exit For
            Else
                Debug.Print "找到子文件夹: " & folder.Name
            End If
        Next
    End If

    Set GetFolderFromPath = folder
    Exit Function

ErrorHandler:
    Debug.Print "错误: " & Err.Description
    MsgBox "获取文件夹时出错: " & Err.Description & vbCrLf & _
           "可用的根文件夹: " & availableFolders, vbExclamation
    Set GetFolderFromPath = Nothing
End Function

Sub DownloadInventory_Each()
    
    Dim myOutlook As New Outlook.Application
    Dim myNameSpace As Outlook.nameSpace
    Dim myFolder As Outlook.folder
    Dim Subfolder As Outlook.folder
    Dim Email As Outlook.mailItem
    Dim Dmi As Outlook.mailItem
    Dim UTCDate As Date
    Dim fileName As String
    Dim i As Integer
    Dim wdDoc As Word.Document
    Dim wdRange As Word.Range
    Dim wdTable As Word.Table
    Dim wdTables As Word.Tables
    Dim StringMess As String
    Dim location As String
    Dim folderPath As String
    
    Set Dmi = myOutlook.CreateItem(olMailItem)
    Set myNameSpace = myOutlook.GetNamespace("MAPI")
    Set wsSetting = ThisWorkbook.Sheets("Settings")
    
    If wsSetting.Range("A5").Value = "" Then
        Set myFolder = myNameSpace.PickFolder
        If Not myFolder Is Nothing Then
            wsSetting.Range("A5").Value = myFolder.folderPath
        End If
    Else
        folderPath = wsSetting.Range("A5").Value
        Set myFolder = GetFolderFromPath(myNameSpace, folderPath) ' 注意：这个方法仅适用于默认的邮箱账户，对于其他账户可能需要调整
    End If
    
    
'    Set MyFolder = MyNamespace.GetDefaultFolder(olFolderInbox).folders("DGP Stock")
'    Set MyFolder = MyNamespace.GetDefaultFolder(olFolderInbox).folders("DGP Stock").folders("test")
'    wsSetting.Columns("A:L").ClearContents
'    Columns("A:L").NumberFormat = "@"
    Summary.Range("A1:C3").ClearContents
    SenderNameOriRow = wsSetting.Cells.Find("*SenderName").Row
    SenderNameOriCol = wsSetting.Cells.Find("*SenderName").Column
    SenderNameColumn = wsSetting.Cells.Find("*SenderName").End(xlDown).Column
    ShortNameColumn = wsSetting.Cells.Find("*Short Name").End(xlDown).Column
    ShortName2Column = wsSetting.Cells.Find("*Short Name2").End(xlDown).Column
    SaveNameColumn = wsSetting.Cells.Find("*SaveName").End(xlDown).Column
    RecvDayColumn = wsSetting.Cells.Find("*RecvDay").End(xlDown).Column
    RecvDay2Column = wsSetting.Cells.Find("*RecvDay2").End(xlDown).Column
    KeywordsColumn = wsSetting.Cells.Find("*Keywords").End(xlDown).Column
    LocationColumn = wsSetting.Cells.Find("*Location").End(xlDown).Column
    AttachementNameColumn = wsSetting.Cells.Find("*AttachementName").End(xlDown).Column
    
    For i = 2 To wsSetting.Cells(SenderNameOriRow, SenderNameOriCol).End(xlDown).Row + 1
        If i >= wsSetting.Cells(i, 1).End(xlDown).Row + 1 Then
           Exit Sub
        End If
        
        SenderName = wsSetting.Cells(i, SenderNameColumn)
        ShortName = wsSetting.Cells(i, ShortNameColumn)
        ShortName2 = wsSetting.Cells(i, ShortName2Column)
        SaveName = wsSetting.Cells(i, SaveNameColumn)
        RecvDay = wsSetting.Cells(i, RecvDayColumn)
        RecvDay2 = wsSetting.Cells(i, RecvDay2Column)
        Keywords = wsSetting.Cells(i, KeywordsColumn)
        location = wsSetting.Cells(i, LocationColumn)
        AttachementName = wsSetting.Cells(i, AttachementNameColumn)
        
        Call CreateFoldersInPath(location)

        ' 不再使用Windows Search索引服务，改为直接遍历邮件
        If Not myFolder Is Nothing Then
            ' 获取邮件总数用于进度显示
            Dim totalItems As Long
            Dim currentItem As Long
            totalItems = myFolder.Items.Count
            currentItem = 0

            ' 显示处理进度
            Application.StatusBar = "正在处理发件人: " & SenderName & " (共 " & totalItems & " 封邮件)"

            ' 直接遍历文件夹中的所有邮件项目
            For Each Email In myFolder.Items
                currentItem = currentItem + 1

                ' 每处理100封邮件更新一次进度
                If currentItem Mod 100 = 0 Then
                    Application.StatusBar = "正在处理发件人: " & SenderName & " (" & currentItem & "/" & totalItems & ")"
                    DoEvents ' 允许界面更新
                End If
                ' 检查邮件类型是否为MailItem
                If TypeOf Email Is Outlook.mailItem Then
                    Dim mailItem As Outlook.mailItem
                    Set mailItem = Email

                    ' 检查发件人是否匹配
                    Dim senderMatch As Boolean
                    senderMatch = False
                    If InStr(1, mailItem.SenderName, SenderName, vbTextCompare) > 0 Or _
                       InStr(1, mailItem.SenderEmailAddress, SenderName, vbTextCompare) > 0 Then
                        senderMatch = True
                    End If

                    ' 检查关键词是否匹配（主题或正文）
                    Dim keywordMatch As Boolean
                    keywordMatch = False
                    If Keywords <> "" Then
                        If InStr(1, mailItem.Subject, Keywords, vbTextCompare) > 0 Or _
                           InStr(1, mailItem.Body, Keywords, vbTextCompare) > 0 Then
                            keywordMatch = True
                        End If
                    Else
                        keywordMatch = True ' 如果没有关键词要求，则认为匹配
                    End If

                    ' 检查是否有附件
                    Dim hasAttachment As Boolean
                    hasAttachment = (mailItem.Attachments.Count > 0)

                    ' 检查接收日期是否在指定范围内
                    Dim dateMatch As Boolean
                    dateMatch = False
                    If mailItem.ReceivedTime >= RecvDay And mailItem.ReceivedTime <= RecvDay2 Then
                        dateMatch = True
                    End If

                    ' 如果所有条件都匹配，则处理附件
                    If senderMatch And keywordMatch And hasAttachment And dateMatch Then
                        For Each myAttachments In mailItem.Attachments
                            If UCase(myAttachments.fileName) Like UCase(AttachementName) Then
                                myAttachments.SaveAsFile location & SaveName
                                Summary.Range(ShortName) = ShortName2
                            End If
                        Next myAttachments
                    End If
                End If
            Next Email

            ' 重置状态栏
            Application.StatusBar = False
        End If
    Next i

    ' 处理完成后重置状态栏
    Application.StatusBar = "邮件处理完成"

    ' 清理对象引用
    Set myOutlook = Nothing
    Set myNameSpace = Nothing
    Set myFolder = Nothing
    Set Dmi = Nothing

End Sub

' 优化版本：使用基本过滤器减少需要检查的邮件数量
Sub DownloadInventory_Optimized()

    Dim myOutlook As New Outlook.Application
    Dim myNameSpace As Outlook.nameSpace
    Dim myFolder As Outlook.folder
    Dim Email As Outlook.mailItem
    Dim Dmi As Outlook.mailItem
    Dim fileName As String
    Dim i As Integer
    Dim StringMess As String
    Dim location As String
    Dim folderPath As String

    Set Dmi = myOutlook.CreateItem(olMailItem)
    Set myNameSpace = myOutlook.GetNamespace("MAPI")
    Set wsSetting = ThisWorkbook.Sheets("Settings")

    If wsSetting.Range("A5").Value = "" Then
        Set myFolder = myNameSpace.PickFolder
        If Not myFolder Is Nothing Then
            wsSetting.Range("A5").Value = myFolder.folderPath
        End If
    Else
        folderPath = wsSetting.Range("A5").Value
        Set myFolder = GetFolderFromPath(myNameSpace, folderPath)
    End If

    Summary.Range("A1:C3").ClearContents
    SenderNameOriRow = wsSetting.Cells.Find("*SenderName").Row
    SenderNameOriCol = wsSetting.Cells.Find("*SenderName").Column
    SenderNameColumn = wsSetting.Cells.Find("*SenderName").End(xlDown).Column
    ShortNameColumn = wsSetting.Cells.Find("*Short Name").End(xlDown).Column
    ShortName2Column = wsSetting.Cells.Find("*Short Name2").End(xlDown).Column
    SaveNameColumn = wsSetting.Cells.Find("*SaveName").End(xlDown).Column
    RecvDayColumn = wsSetting.Cells.Find("*RecvDay").End(xlDown).Column
    RecvDay2Column = wsSetting.Cells.Find("*RecvDay2").End(xlDown).Column
    KeywordsColumn = wsSetting.Cells.Find("*Keywords").End(xlDown).Column
    LocationColumn = wsSetting.Cells.Find("*Location").End(xlDown).Column
    AttachementNameColumn = wsSetting.Cells.Find("*AttachementName").End(xlDown).Column

    For i = 2 To wsSetting.Cells(SenderNameOriRow, SenderNameOriCol).End(xlDown).Row + 1
        If i >= wsSetting.Cells(i, 1).End(xlDown).Row + 1 Then
           Exit Sub
        End If

        SenderName = wsSetting.Cells(i, SenderNameColumn)
        ShortName = wsSetting.Cells(i, ShortNameColumn)
        ShortName2 = wsSetting.Cells(i, ShortName2Column)
        SaveName = wsSetting.Cells(i, SaveNameColumn)
        RecvDay = wsSetting.Cells(i, RecvDayColumn)
        RecvDay2 = wsSetting.Cells(i, RecvDay2Column)
        Keywords = wsSetting.Cells(i, KeywordsColumn)
        location = wsSetting.Cells(i, LocationColumn)
        AttachementName = wsSetting.Cells(i, AttachementNameColumn)

        Call CreateFoldersInPath(location)

        ' 使用基本的日期过滤器来减少需要检查的邮件数量
        If Not myFolder Is Nothing Then
            Dim filteredItems As Outlook.Items
            Dim basicFilter As String

            ' 创建基本的日期过滤器
            basicFilter = "[ReceivedTime] >= '" & Format(RecvDay, "mm/dd/yyyy") & "' AND [ReceivedTime] <= '" & Format(RecvDay2, "mm/dd/yyyy") & "'"

            ' 应用基本过滤器
            Set filteredItems = myFolder.Items.Restrict(basicFilter)

            ' 获取过滤后的邮件总数
            Dim totalFilteredItems As Long
            Dim currentFilteredItem As Long
            totalFilteredItems = filteredItems.Count
            currentFilteredItem = 0

            Application.StatusBar = "正在处理发件人: " & SenderName & " (已过滤到 " & totalFilteredItems & " 封邮件)"

            ' 遍历过滤后的邮件
            For Each Email In filteredItems
                currentFilteredItem = currentFilteredItem + 1

                ' 每处理50封邮件更新一次进度
                If currentFilteredItem Mod 50 = 0 Then
                    Application.StatusBar = "正在处理发件人: " & SenderName & " (" & currentFilteredItem & "/" & totalFilteredItems & ")"
                    DoEvents
                End If

                ' 检查邮件类型
                If TypeOf Email Is Outlook.mailItem Then
                    Dim mailItem As Outlook.mailItem
                    Set mailItem = Email

                    ' 检查发件人
                    Dim senderMatch As Boolean
                    senderMatch = False
                    If InStr(1, mailItem.SenderName, SenderName, vbTextCompare) > 0 Or _
                       InStr(1, mailItem.SenderEmailAddress, SenderName, vbTextCompare) > 0 Then
                        senderMatch = True
                    End If

                    ' 检查关键词
                    Dim keywordMatch As Boolean
                    keywordMatch = False
                    If Keywords <> "" Then
                        If InStr(1, mailItem.Subject, Keywords, vbTextCompare) > 0 Or _
                           InStr(1, mailItem.Body, Keywords, vbTextCompare) > 0 Then
                            keywordMatch = True
                        End If
                    Else
                        keywordMatch = True
                    End If

                    ' 检查附件
                    Dim hasAttachment As Boolean
                    hasAttachment = (mailItem.Attachments.Count > 0)

                    ' 处理匹配的邮件
                    If senderMatch And keywordMatch And hasAttachment Then
                        For Each myAttachments In mailItem.Attachments
                            If UCase(myAttachments.fileName) Like UCase(AttachementName) Then
                                myAttachments.SaveAsFile location & SaveName
                                Summary.Range(ShortName) = ShortName2
                            End If
                        Next myAttachments
                    End If
                End If
            Next Email

            Application.StatusBar = False
        End If
    Next i

    Application.StatusBar = "邮件处理完成"

    ' 清理对象引用
    Set myOutlook = Nothing
    Set myNameSpace = Nothing
    Set myFolder = Nothing
    Set filteredItems = Nothing
    Set Dmi = Nothing

End Sub
