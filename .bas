Function CreateFoldersInPath(location As String) As Boolean
    Dim folders() As String
    Dim folderPath As String
    Dim folder As String
    Dim i As Integer
     
    If location <> "" Then
    
        folders = Split(location, "\")
        folderPath = folders(0) & "\"
        
        For i = 1 To UBound(folders)
            folder = folders(i)
            If folder <> "" Then
                folderPath = folderPath & folder & "\"
                If Dir(folderPath, vbDirectory) = "" Then
                    MkDir folderPath
                End If
            End If
        Next i
    
    End If
    
End Function
Function GetFolderFromPath(myNameSpace As Outlook.nameSpace, folderPath As String) As Outlook.folder
    Dim FoldersArray As Variant
    Dim i As Integer
    Dim folder As Outlook.folder
    ' 移除路径开头的两个反斜杠（如果存在）
    If Left(folderPath, 2) = "\\" Then
        folderPath = Right(folderPath, Len(folderPath) - 2)
    End If
    FoldersArray = Split(folderPath, "\")
    On Error GoTo ErrorHandler
    ' 从MyNamespace.Folders集合获取第一个邮箱账户名称对应的根文件夹
    Set folder = myNameSpace.folders(FoldersArray(0))
    If Not folder Is Nothing Then
        ' 遍历余下的文件夹层级
        For i = 1 To UBound(FoldersArray, 1)
            Set folder = folder.folders(FoldersArray(i))
            If folder Is Nothing Then
                Exit For
            End If
        Next
    End If
    Set GetFolderFromPath = folder
    Exit Function

ErrorHandler:
    Set GetFolderFromPath = Nothing
End Function

Sub DownloadInventory_Each()
    
    Dim myOutlook As New Outlook.Application
    Dim myNameSpace As Outlook.nameSpace
    Dim myFolder As Outlook.folder
    Dim Subfolder As Outlook.folder
    Dim Email As Outlook.mailItem
    Dim Dmi As Outlook.mailItem
    Dim UTCDate As Date
    Dim fileName As String
    Dim i As Integer
    Dim wdDoc As Word.Document
    Dim wdRange As Word.Range
    Dim wdTable As Word.Table
    Dim wdTables As Word.Tables
    Dim StringMess As String
    Dim location As String
    Dim folderPath As String
    
    Set Dmi = myOutlook.CreateItem(olMailItem)
    Set myNameSpace = myOutlook.GetNamespace("MAPI")
    Set wsSetting = ThisWorkbook.Sheets("Settings")
    
    If wsSetting.Range("A5").Value = "" Then
        Set myFolder = myNameSpace.PickFolder
        If Not myFolder Is Nothing Then
            wsSetting.Range("A5").Value = myFolder.folderPath
        End If
    Else
        folderPath = wsSetting.Range("A5").Value
        Set myFolder = GetFolderFromPath(myNameSpace, folderPath) ' 注意：这个方法仅适用于默认的邮箱账户，对于其他账户可能需要调整
    End If
    
    
'    Set MyFolder = MyNamespace.GetDefaultFolder(olFolderInbox).folders("DGP Stock")
'    Set MyFolder = MyNamespace.GetDefaultFolder(olFolderInbox).folders("DGP Stock").folders("test")
'    wsSetting.Columns("A:L").ClearContents
'    Columns("A:L").NumberFormat = "@"
    Summary.Range("A1:C3").ClearContents
    SenderNameOriRow = wsSetting.Cells.Find("*SenderName").Row
    SenderNameOriCol = wsSetting.Cells.Find("*SenderName").Column
    SenderNameColumn = wsSetting.Cells.Find("*SenderName").End(xlDown).Column
    ShortNameColumn = wsSetting.Cells.Find("*Short Name").End(xlDown).Column
    ShortName2Column = wsSetting.Cells.Find("*Short Name2").End(xlDown).Column
    SaveNameColumn = wsSetting.Cells.Find("*SaveName").End(xlDown).Column
    RecvDayColumn = wsSetting.Cells.Find("*RecvDay").End(xlDown).Column
    RecvDay2Column = wsSetting.Cells.Find("*RecvDay2").End(xlDown).Column
    KeywordsColumn = wsSetting.Cells.Find("*Keywords").End(xlDown).Column
    LocationColumn = wsSetting.Cells.Find("*Location").End(xlDown).Column
    AttachementNameColumn = wsSetting.Cells.Find("*AttachementName").End(xlDown).Column
    
    For i = 2 To wsSetting.Cells(SenderNameOriRow, SenderNameOriCol).End(xlDown).Row + 1
        If i >= wsSetting.Cells(i, 1).End(xlDown).Row + 1 Then
           Exit Sub
        End If
        
        SenderName = wsSetting.Cells(i, SenderNameColumn)
        ShortName = wsSetting.Cells(i, ShortNameColumn)
        ShortName2 = wsSetting.Cells(i, ShortName2Column)
        SaveName = wsSetting.Cells(i, SaveNameColumn)
        RecvDay = wsSetting.Cells(i, RecvDayColumn)
        RecvDay2 = wsSetting.Cells(i, RecvDay2Column)
        Keywords = wsSetting.Cells(i, KeywordsColumn)
        location = wsSetting.Cells(i, LocationColumn)
        AttachementName = wsSetting.Cells(i, AttachementNameColumn)
        
        Call CreateFoldersInPath(location)
        UTCDateRecvDay = Dmi.PropertyAccessor.LocalTimeToUTC(RecvDay)
        UTCDateRecvDay2 = Dmi.PropertyAccessor.LocalTimeToUTC(RecvDay2)

        FilterString = "@SQL=(((""http://schemas.microsoft.com/mapi/proptag/0x0065001f"" CI_STARTSWITH '%" & SenderName & "%' OR ""http://schemas.microsoft.com/mapi/proptag/0x0042001f"" CI_STARTSWITH '%" & SenderName & "%')" _
         & "AND (""http://schemas.microsoft.com/mapi/proptag/0x0037001f"" LIKE '%" & Keywords & "%'" _
         & "OR ""http://schemas.microsoft.com/mapi/proptag/0x1000001f"" LIKE '%" & Keywords & "%')" _
         & "AND ""urn:schemas:httpmail:hasattachment"" = 1" _
         & "AND (""urn:schemas:httpmail:datereceived"" >= '" & UTCDateRecvDay & "'" _
         & "AND ""urn:schemas:httpmail:datereceived"" <= '" & UTCDateRecvDay2 & "')))"
'
        If Not myFolder Is Nothing Then
                
                For Each Email In myFolder.Items.Restrict(FilterString)
                    
                        For Each myAttachments In Email.Attachments
   
                                If UCase(myAttachments.fileName) Like UCase(AttachementName) Then
                                
                                    myAttachments.SaveAsFile location & SaveName
                                    Summary.Range(ShortName) = ShortName2
                    
                                End If
                             
                        Next myAttachments

                Next Email

        End If
    Next i

End Sub
