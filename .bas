Sub DownloadInventory_Each()

    Dim myOutlook As New Outlook.Application
    Dim myNameSpace As Outlook.nameSpace
    Dim myFolder As Outlook.Folder
    Dim Subfolder As Outlook.Folder
    Dim Email As Outlook.MailItem
    Dim Dmi As Outlook.MailItem
    Dim UTCDate As Date
    Dim fileName As String
    Dim i As Integer
    Dim wdDoc As Word.Document
    Dim wdRange As Word.Range
    Dim wdTable As Word.Table
    Dim wdTables As Word.Tables
    Dim StringMess As String
    Dim location As String
    Dim folderPath As String
    Dim emailCount As Long
    Dim processedCount As Long

    Set Dmi = myOutlook.CreateItem(olMailItem)
    Set myNameSpace = myOutlook.GetNamespace("MAPI")
    Set wsSetting = ThisWorkbook.Sheets("Settings")

    If wsSetting.Range("A5").Value = "" Then
        Set myFolder = myNameSpace.PickFolder
        If Not myFolder Is Nothing Then
            wsSetting.Range("A5").Value = myFolder.folderPath
        End If
    Else
        folderPath = wsSetting.Range("A5").Value
        Set myFolder = GetFolderFromPath(myNameSpace, folderPath) ' 注意：这个方法仅适用于默认的邮箱账户，对于其他账户可能需要调整
    End If


'    Set MyFolder = MyNamespace.GetDefaultFolder(olFolderInbox).folders("DGP Stock")
'    Set MyFolder = MyNamespace.GetDefaultFolder(olFolderInbox).folders("DGP Stock").folders("test")
'    wsSetting.Columns("A:L").ClearContents
'    Columns("A:L").NumberFormat = "@"
    Summary.Range("A1:C3").ClearContents
    SenderNameOriRow = wsSetting.Cells.Find("*SenderName").Row
    SenderNameOriCol = wsSetting.Cells.Find("*SenderName").Column
    SenderNameColumn = wsSetting.Cells.Find("*SenderName").End(xlDown).Column
    ShortNameColumn = wsSetting.Cells.Find("*Short Name").End(xlDown).Column
    ShortName2Column = wsSetting.Cells.Find("*Short Name2").End(xlDown).Column
    SaveNameColumn = wsSetting.Cells.Find("*SaveName").End(xlDown).Column
    RecvDayColumn = wsSetting.Cells.Find("*RecvDay").End(xlDown).Column
    RecvDay2Column = wsSetting.Cells.Find("*RecvDay2").End(xlDown).Column
    KeywordsColumn = wsSetting.Cells.Find("*Keywords").End(xlDown).Column
    LocationColumn = wsSetting.Cells.Find("*Location").End(xlDown).Column
    AttachementNameColumn = wsSetting.Cells.Find("*AttachementName").End(xlDown).Column

    For i = 2 To wsSetting.Cells(SenderNameOriRow, SenderNameOriCol).End(xlDown).Row + 1
        If i >= wsSetting.Cells(i, 1).End(xlDown).Row + 1 Then
           Exit Sub
        End If

        SenderName = wsSetting.Cells(i, SenderNameColumn)
        ShortName = wsSetting.Cells(i, ShortNameColumn)
        ShortName2 = wsSetting.Cells(i, ShortName2Column)
        SaveName = wsSetting.Cells(i, SaveNameColumn)
        RecvDay = wsSetting.Cells(i, RecvDayColumn)
        RecvDay2 = wsSetting.Cells(i, RecvDay2Column)
        Keywords = wsSetting.Cells(i, KeywordsColumn)
        location = wsSetting.Cells(i, LocationColumn)
        AttachementName = wsSetting.Cells(i, AttachementNameColumn)

        Call CreateFoldersInPath(location)

        ' 移除Windows Search索引服务依赖，改用直接遍历方式
        If Not myFolder Is Nothing Then
            emailCount = myFolder.Items.Count
            processedCount = 0

            ' 直接遍历文件夹中的所有邮件项目，不使用Windows Search索引
            For Each Email In myFolder.Items
                processedCount = processedCount + 1

                ' 显示进度（可选）
                If processedCount Mod 100 = 0 Then
                    Application.StatusBar = "正在处理邮件 " & processedCount & " / " & emailCount & " (" & SenderName & ")"
                    DoEvents
                End If

                ' 检查邮件类型是否为MailItem
                If TypeName(Email) = "MailItem" Then
                    ' 手动检查邮件是否符合条件，替代Windows Search索引过滤
                    If CheckEmailCriteria(Email, SenderName, Keywords, RecvDay, RecvDay2) Then
                        ' 检查是否有附件
                        If Email.Attachments.Count > 0 Then
                            For Each myAttachments In Email.Attachments
                                If UCase(myAttachments.fileName) Like UCase(AttachementName) Then
                                    myAttachments.SaveAsFile location & SaveName
                                    Summary.Range(ShortName) = ShortName2
                                End If
                            Next myAttachments
                        End If
                    End If
                End If
            Next Email

            ' 清除状态栏
            Application.StatusBar = False
        End If
    Next i

End Sub

' 新增函数：检查邮件是否符合条件（替代Windows Search索引过滤）
Private Function CheckEmailCriteria(Email As Outlook.MailItem, SenderName As String, Keywords As String, RecvDay As Date, RecvDay2 As Date) As Boolean
    Dim senderMatch As Boolean
    Dim keywordMatch As Boolean
    Dim dateMatch As Boolean

    ' 检查发件人（发件人姓名或邮箱地址）
    senderMatch = (InStr(1, Email.SenderName, SenderName, vbTextCompare) > 0) Or _
                  (InStr(1, Email.SenderEmailAddress, SenderName, vbTextCompare) > 0)

    ' 检查关键词（主题或正文）
    If Keywords <> "" Then
        keywordMatch = (InStr(1, Email.Subject, Keywords, vbTextCompare) > 0) Or _
                       (InStr(1, Email.Body, Keywords, vbTextCompare) > 0)
    Else
        keywordMatch = True ' 如果没有关键词要求，则认为匹配
    End If

    ' 检查接收日期范围
    dateMatch = (Email.ReceivedTime >= RecvDay) And (Email.ReceivedTime <= RecvDay2)

    ' 所有条件都满足才返回True
    CheckEmailCriteria = senderMatch And keywordMatch And dateMatch
End Function



